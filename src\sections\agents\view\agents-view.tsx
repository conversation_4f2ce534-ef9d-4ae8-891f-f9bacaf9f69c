import { Box, CircularProgress, Typography, Stack, Grid, Divider, Tab } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { paths } from 'src/routes/paths';
import { AppButton, AppContainer } from 'src/components/common';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { Template } from 'src/services/api/use-templates-api';
import { CustomTabs } from 'src/components/custom-tabs';
import AgentSearchBar from '../components/agent-search-bar';
import CategoryFilter from '../components/category-filter';
import { useAgentView } from './use-agent-view';

// ----------------------------------------------------------------------

export const AgentsView = () => {
  // Use the custom hook for all logic
  const {
    openConfirmDialog,
    table,
    templates,
    loading,
    error,
    isPending,
    searchQuery,
    categoryFilter,
    categoryOptions,
    typeFilter,
    typeOptions,
    toolFilter,
    toolOptions,
    modelFilter,
    modelOptions,
    statusFilter,
    statusOptions,
    activeTab,
    tabs,
    handleCloseConfirmDialog,
    handleConfirmDelete,
    handleSearchChange,
    handleCategoryChange,
    handleTypeChange,
    handleToolChange,
    handleModelChange,
    handleStatusChange,
    handleTabChange,
    columns,
  } = useAgentView();

  return (
    <AppContainer
      title="Agent's Template"
      pageTitle="Agent's Template"
      buttons={[
        {
          label: 'New Agent`s Template',
          variant: 'contained',
          startIcon: <Iconify icon="majesticons:plus" />,
          href: paths.dashboard.agents.create,
        },
      ]}
    >
      <Divider />
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Please try again later
          </Typography>
        </Box>
      ) : (
        <Box sx={{ mt: 3 }}>
          {/* Tabs Section */}
          <Box sx={{ mb: 3 }}>
            <CustomTabs value={activeTab} onChange={handleTabChange}>
              {tabs.map((tab) => (
                <Tab key={tab.value} value={tab.value} label={tab.label} />
              ))}
            </CustomTabs>
          </Box>

          {/* Search and Filter Section */}
          <Stack spacing={3} sx={{ mb: 3 }}>
            <Grid container spacing={2}>
              <Grid  item xs={12} md={12}>
                <AgentSearchBar
                  query={searchQuery}
                  sx={{
                    bgcolor: (theme) => theme.vars.palette.background.neutral,
                    borderRadius: '4px',
                  }}
                  onChange={handleSearchChange}
                  placeholder="Search templates..."
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={categoryFilter}
                  onChange={handleCategoryChange}
                  options={categoryOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={typeFilter}
                  onChange={handleTypeChange}
                  options={typeOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={toolFilter}
                  onChange={handleToolChange}
                  options={toolOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={modelFilter}
                  onChange={handleModelChange}
                  options={modelOptions}
                  label=""
                />
              </Grid>
              <Grid item xs={12} md={1}>
                <CategoryFilter
                  value={statusFilter}
                  onChange={handleStatusChange}
                  options={statusOptions}
                  label=""
                />
              </Grid>
            </Grid>
          </Stack>

          <AppTable<Template>
            headLabels={[
              { id: 'name', label: 'Name' },
              { id: 'createdAt', label: 'Date Created' },
              { id: 'type', label: 'Type' },
              { id: 'category', label: 'Category' },
              { id: 'templateTools', label: 'Tools' },
              { id: 'model', label: 'LLM Model' },
              { id: 'status', label: 'Status' },
              { id: 'actions', label: 'Action' },
            ]}
            dataCount={templates.length}
            data={templates}
            columns={columns}
            table={table}
            noDataLabel="No templates found"
          />
        </Box>
      )}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        title="Delete Template"
        content="Are you sure you want to delete this template? This action cannot be undone."
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton label="Cancel" variant="outlined" onClick={handleCloseConfirmDialog} />
            <AppButton
              isLoading={isPending}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
            />
          </Box>
        }
      />
    </AppContainer>
  );
};
