import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useToolsApi } from 'src/services/api/use-tools-api';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';
import { useNavigate } from 'react-router-dom';
import { paths } from 'src/routes/paths';

// Form validation schema for template (agent)
const agentSchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    description: z.string().min(1, 'Description is required'),
    systemPrompt: z.string().min(1, 'System prompt is required'),
    systemMessage: z.string().min(1, 'Description is required'),
    type: z.enum(['SINGLE', 'MULTI'], {
      required_error: 'Type is required',
    }),
    status: z.enum(['ACTIVE', 'DISABLED'], {
      required_error: 'Status is required',
    }),
    category: z.string().min(1, 'Category is required'),
    toolsId: z.array(z.number()).min(1, 'please choose at least one tool').default([]),
    model: z.enum(['GPT_4O_MINI', 'GPT_4O', 'CLAUDE_3_7_SONNET', 'GEMINI_2_0_FLASH', 'GEMINI_1_5_FLASH'], {
      required_error: 'Model is required',
    }),
    channels: z.array(z.string()).min(1, 'Please choose at least one channel').default([]),
    categoryId: z.coerce.number().optional(),
  })
  .transform((data) => ({
    ...data,
    categoryId: Number(data.category),
  }));

// Form values type
export type AgentFormValues = z.infer<typeof agentSchema>;

// Agent type (template) - using Template from API
export type Agent = Template;

// Define the available type options as constants
export const TYPE_OPTIONS = [
  { value: 'SINGLE', label: 'Single Agent' },
  { value: 'MULTI', label: 'Team Agent' },
];

// Define the available status options as constants
export const STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'DISABLED', label: 'Disabled' },
];

export const LLM_MODEL = [
  {
    label: 'GPT-4o Mini',
    value: 'GPT_4O_MINI',
    icon: 'hugeicons:chat-gpt',
    description: 'Fast and efficient model for simple tasks',
    provider: 'OpenAI'
  },
  {
    label: 'GPT-4o',
    value: 'GPT_4O',
    icon: 'arcticons:openai-chatgpt',
    description: 'Advanced model for complex reasoning tasks',
    provider: 'OpenAI'
  },
  {
    label: 'Claude 3.5 Sonnet',
    value: 'CLAUDE_3_7_SONNET',
    icon: 'simple-icons:anthropic',
    description: 'Anthropic\'s most intelligent model for complex tasks',
    provider: 'Anthropic'
  },
  {
    label: 'Gemini 2.0 Flash',
    value: 'GEMINI_2_0_FLASH',
    icon: 'ri:gemini-fill',
    description: 'Google\'s latest multimodal AI model',
    provider: 'Google'
  },
  {
    label: 'Gemini 1.5 Flash',
    value: 'GEMINI_1_5_FLASH',
    icon: 'ri:gemini-line',
    description: 'Fast and efficient multimodal model',
    provider: 'Google'
  },
];
export const CHANNEL_OPTIONS = [
  { value: 'SLACK', label: 'Slack', icon: 'logos:slack-icon' },
  { value: 'EMAIL', label: 'Email', icon: 'mdi:email-outline' },
  { value: 'WEB', label: 'Web', icon: 'mdi:web' },
  { value: 'API', label: 'API', icon: 'mdi:api' },
];

interface UseAgentFormProps {
  agent: Agent | null;
}

export function useAgentForm({ agent }: UseAgentFormProps) {
  const theme = useTheme();

  // Get categories and tools data from APIs
  const { useGetCategories } = useCategoriesApi();
  const { useGetTools } = useToolsApi();
  const { useCreateTemplate, useUpdateTemplate } = useTemplatesApi();
  const navigate = useNavigate();

  const { data: categoriesResponse } = useGetCategories();
  const { data: toolsResponse } = useGetTools();
  const { mutate: createTemplate, isPending: isCreating } = useCreateTemplate();
  const { mutate: updateTemplate, isPending: isUpdating } = useUpdateTemplate(agent?.id || 0);

  const categories = categoriesResponse?.categories || [];
  const tools = toolsResponse?.tools || [];
  const isLoading = isCreating || isUpdating;

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // State for search queries
  const [toolSearchQuery, setToolSearchQuery] = useState('');
  const [categorySearchQuery, setCategorySearchQuery] = useState('');
  const [channelSearchQuery, setChannelSearchQuery] = useState('');

  // Handle search changes
  const handleToolSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setToolSearchQuery(event.target.value);
  }, []);

  const handleCategorySearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategorySearchQuery(event.target.value);
  }, []);

  const handleChannelSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setChannelSearchQuery(event.target.value);
  }, []);

  // Filter available tools based on search query
  const filteredTools = toolSearchQuery
    ? tools.filter((tool) => tool.name.toLowerCase().includes(toolSearchQuery.toLowerCase()))
    : tools;

  // Filter categories based on search query
  const filteredCategories = categorySearchQuery
    ? categories.filter((category) =>
        category.name.toLowerCase().includes(categorySearchQuery.toLowerCase())
      )
    : categories;

  // Filter channels based on search query
  const filteredChannels = channelSearchQuery
    ? LLM_MODEL.filter((channel) =>
        channel.label.toLowerCase().includes(channelSearchQuery.toLowerCase())
      )
    : LLM_MODEL;

  // Initialize form with default values or agent data for editing
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentSchema),
    defaultValues: {
      name: '',
      description: '',
      systemPrompt: '',
      type: 'SINGLE' as const,
      status: 'ACTIVE',
      category: '',
      toolsId: [],
      model: 'GPT_4O_MINI' as const,
      channels: [],
    },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    watch,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Reset form when agent changes
  useEffect(() => {
    if (agent) {
      // Extract tool IDs from templateTools array
      const toolsId = agent.templateTools?.map(tt => tt.tool.id) || [];

      // Pre-fill form with agent data for editing
      reset({
        name: agent.name,
        description: agent.description,
        systemPrompt: agent.systemMessage,
        type: agent.type,
        status: agent.status,
        category: agent.categoryId.toString(),
        toolsId,
        model: agent.model, // Include the model field from the agent data
        channels: [], // Default empty array since channels aren't in Template interface
      });
    } else {
      // Reset to default values for creating new agent
      reset({
        name: '',
        description: '',
        systemPrompt: '',
        type: 'SINGLE' as const,
        status: 'ACTIVE',
        category: '',
        toolsId: [],
        model: 'GPT_4O_MINI' as const, // Set default model
        channels: [],
      });
    }
  }, [agent, reset]);

  // Watch for changes in the tools array
  const selectedTools = watch('toolsId');
  const selectedChannels = watch('channels');
  const selectedModel = watch('model');

  // Handle next step
  const handleNext = async () => {
    let fieldsToValidate: (keyof AgentFormValues)[] = [];

    if (activeStep === 0) {
      fieldsToValidate = ['name', 'description', 'systemPrompt', 'type', 'status'];
    } else if (activeStep === 1) {
      fieldsToValidate = ['categoryId'];
    } else if (activeStep === 2) {
      fieldsToValidate = ['toolsId'];
    } else if (activeStep === 3) {
      fieldsToValidate = ['model'];
    }

    const isStepValid = await trigger(fieldsToValidate);
    if (isStepValid) {
      if (activeStep < 4) {
        // 5 steps total, 0-indexed
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle tool selection
  const handleToolToggle = (toolId: number) => {
    const currentTools = [...selectedTools];
    const toolIndex = currentTools.indexOf(toolId);

    if (toolIndex === -1) {
      // Add the tool
      currentTools.push(toolId);
    } else {
      // Remove the tool
      currentTools.splice(toolIndex, 1);
    }

    setValue('toolsId', currentTools);
  };

  const handleChannelToggle = (channelValue: string) => {
    const currentChannels = [...selectedChannels];
    const channelIndex = currentChannels.indexOf(channelValue);

    if (channelIndex === -1) {
      currentChannels.push(channelValue);
    } else {
      currentChannels.splice(channelIndex, 1);
    }

    setValue('channels', currentChannels);
  };

  const handleModelSelect = (modelValue: 'GPT_4O_MINI' | 'GPT_4O' | 'CLAUDE_3_7_SONNET' | 'GEMINI_2_0_FLASH' | 'GEMINI_1_5_FLASH') => {
    setValue('model', modelValue);
  };

  // Handle form submission
  const onFormSubmit = handleSubmit((data: AgentFormValues) => {
    const { name, description, systemPrompt, type, status, categoryId, toolsId, model } = data;
    const submissionData = {
      name,
      description,
      type,
      status,
      categoryId,
      toolsId,
      systemMessage: systemPrompt,
      model, 
    };
    if (agent) {
      // Update existing template
      updateTemplate(submissionData, {
        onSuccess: () => {
          navigate(paths.dashboard.agents.root);
          reset();
        },
        onError: (error) => {
          console.error('Failed to update template:', error);
        },
      });
    } else {
      // Create new template
      createTemplate(submissionData, {
        onSuccess: () => {
          navigate(paths.dashboard.agents.root);
          reset();
        },
        onError: (error) => {
          console.error('Failed to create template:', error);
        },
      });
    }
  });

  const options: { label: string; value: string; icon: string }[] =
    filteredCategories?.map((category) => ({
      label: category.name,
      icon: category.icon.toString(),
      value: category.id.toString(),
    })) || [];

  return {
    theme,
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    handleSubmit,
    availableTools: filteredTools,
    categories: filteredCategories,
    tools,
    TYPE_OPTIONS,
    STATUS_OPTIONS,
    isLoading,
    options,
    selectedChannels,
    handleChannelToggle,
    selectedModel,
    handleModelSelect,
    LLM_MODEL: filteredChannels,
    toolSearchQuery,
    categorySearchQuery,
    channelSearchQuery,
    handleToolSearchChange,
    handleCategorySearchChange,
    handleChannelSearchChange,
  };
}
