import {
  St<PERSON>,
  I<PERSON><PERSON><PERSON>on,
  <PERSON>po<PERSON>,
  <PERSON>,
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { Form } from 'src/components/hook-form/form-provider';
import { AppB<PERSON>on, AppContainer } from 'src/components/common';
import { FormErrorBoundary } from 'src/components/error-boundary';
import { paths } from 'src/routes/paths';
import { Template } from 'src/services/api/use-templates-api';
import { useAgentForm } from './hooks/use-agent-form';
import { DetailsStep, CategoryStep, ToolsStep, ModelStep } from './steps';

// ----------------------------------------------------------------------

interface AgentFormImprovedProps {
  agent: Template | null;
}

export function AgentForm({ agent }: AgentFormImprovedProps) {
  const { t } = useTranslation();
  
  const {
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    handleNext,
    handleBack,
    onFormSubmit,
    steps,
    isLastStep,
  } = useAgentForm({ agent });

  // Step components mapping with original styling
  const stepComponents = [
    <DetailsStep key="details" />,
    <CategoryStep key="category" />,
    <ToolsStep key="tools" />,
    <ModelStep key="model" />,
  ];

  // Create steps array with original structure
  const stepsConfig = steps.map((step, index) => ({
    label: step.label,
    content: stepComponents[index],
  }));
  
  return (
    <FormErrorBoundary>
      {/* Back Navigation - Original Style */}
      <Stack onClick={() => window.history.back()} direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
        <IconButton  >
          <Iconify icon="mdi:arrow-left" />
        </IconButton>
        <Typography variant="body1">Back</Typography>
      </Stack>

      <AppContainer
        title={isEditing ? 'Edit Agent Template' : 'Create New Agents Template'}
        routeLinks={[
          { name: "Agent's Templates", href: paths.dashboard.agents.root },
          { name: isEditing ? 'Edit Agent Template' : 'Create New Agents Template' },
        ]}
       
      >
        <Form methods={methods} onSubmit={onFormSubmit}>
          {/* Original Stepper Design */}
          <Stepper
            activeStep={activeStep}
            alternativeLabel
            sx={{
              my: 3,
              '& .MuiStepConnector-line': {
                mt: '20px',
              },
            }}
          >
            {stepsConfig.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  sx={{
                    pt: 2,
                    mx: 8.5,
                    borderRadius: 5,
                    ...(activeStep === index && {
                      bgcolor: 'rgba(163, 139, 233, 0.13)',
                      width: '65%',
                      color: 'common.white',
                      '& .MuiStepLabel-label': {
                        color: 'common.black',
                      },
                      '& .MuiStepConnector-line': {
                        borderColor: 'primary.main',
                        borderTopWidth: 2,
                      },
                      '& .MuiStepIcon-root': {
                        color: 'white',
                        border: '2px solid',
                        borderColor: 'primary.main',
                        borderRadius: '50%',
                      },
                    }),
                    ...(index < activeStep && {
                      '& .MuiStepIcon-root': {
                        color: 'success.main',
                        bgcolor: 'common.white',
                        borderRadius: '50%',
                      },
                    }),
                  }}
                >
                  {step.label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Original Container Design */}
          <Container>
            <Box
              sx={{
                p: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'divider',
                borderRadius: 1,
              }}
            >
              {stepsConfig[activeStep].content}
            </Box>
          </Container>

          {/* Original Navigation Buttons */}
          <Stack direction="row" justifyContent="space-between" sx={{ mt: 3 }}>
            <Box sx={{ flexGrow: 1 }} />
            {!isLastStep ? (
              <Button
                variant="contained"
                sx={{ backgroundColor: 'primary.main', width: '10vw' }}
                onClick={handleNext}
              >
                Next
              </Button>
            ) : (
              <AppButton
              sx={{width:'15%',my:'20px'}}
                label={isEditing ? 'Update Template' : 'Create Template'}
                variant="contained"
                onClick={onFormSubmit}
                isLoading={isLoading}
              />
            )}
          </Stack>
        </Form>
      </AppContainer>
    </FormErrorBoundary>
  );
}

export default AgentForm;
